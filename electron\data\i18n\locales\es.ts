/**
 * Spanish language pack
 */
export default {
  // Error messages
  errors: {
    success: "Éxito",
    deviceNotConnected: "Dispositivo no conectado",
    invalidParam: "Parámetro inválido",
    operateFailed: "Operación fallida",
    noData: "Sin datos",
    internalError: "Error interno",
    connectionExists: "La conexión ya existe",
    fileContentEmpty: "El contenido del archivo está vacío",
    deviceNotConnectedOrDisconnected: "Dispositivo no conectado o desconectado.",
    getServiceErrorInfo: "No se pudo obtener la información de error correspondiente",
    saveReportFileError: "Error al guardar el archivo de informe rpt",
    getConfigureListError: "Error al obtener la lista de configuración",
    loadConfigureError: "Error al cargar la configuración",
    cancelUploadError: "Error al cancelar la carga del archivo de onda",
    openWaveFileError: "Error al abrir el archivo de onda",
    getFileDataSuccess: "¡Contenido de datos del archivo recuperado con éxito!"
  },
  
  // Log messages
  logs: {
    reportController: {
      getCommonReportListEntry: "getCommonReportList parámetros de entrada",
      getCommonReportListReturn: "getCommonReportList retorno",
      getCommonReportListError: "getCommonReportList error",
      cancelUploadStart: "Cancelar carga de archivo de onda iniciada",
      cancelUploadError: "Error al cancelar carga de archivo de onda",
      openWaveFileStart: "Abrir archivo de onda iniciado",
      openWaveFileError: "Error al abrir archivo de onda"
    },
    configureService: {
      getConfigureListError: "Error al obtener lista de configuración",
      loadConfigureError: "Error al cargar configuración"
    },
    paramService: {
      getDiffParamComplete: "Comparación completada, número de grupos de diferencias",
      getAllDiffParamError: "getAllDiffParam error"
    }
  },
  
  // Common messages
  common: {
    start: "Iniciado",
    stop: "Detenido",
    other: "Otro",
    loading: "Cargando...",
    success: "Éxito",
    failed: "Fallido",
    cancel: "Cancelar",
    confirm: "Confirmar"
  }
};
