"use strict";
import {
  ConfigureInfo,
  LoadConfigureInfo,
  openConfigureInfo,
  SaveConfigureInfo,
} from "../../interface/hmi/configureinfo";
import { app as electronApp, shell } from "electron";
import * as nodePath from "node:path";
import IECCONSTANTS from "../../data/debug/iecConstants";
import fs from "fs";
import fsPromise from "node:fs/promises";
import { getUUID, deleteFolderRecursive } from "../../utils/common";
import { find, filter, remove } from "lodash";
import { t } from "../../data/i18n/i18n";

class ConfigureService {
  cfgPath = nodePath.join(IECCONSTANTS.PATH_CONFIGURE_CFG);

  // 获取组态列表
  async getConfigureList(): Promise<ConfigureInfo[]> {
    try {
      if (!fs.existsSync(this.cfgPath)) {
        const emptyCfg = [];
        await fsPromise.mkdir(IECCONSTANTS.PATH_CONFIGURE, {
          recursive: true,
        });
        fs.writeFileSync(this.cfgPath, JSON.stringify(emptyCfg));
      }
      const data: ConfigureInfo[] = JSON.parse(
        fs.readFileSync(this.cfgPath, "utf8")
      );
      return data;
    } catch (error) {
      console.error(t("logs.configureService.getConfigureListError"), error);
      throw error;
    }
  }

  // 新增组态
  async addConfigure(req: ConfigureInfo): Promise<ConfigureInfo[]> {
    try {
      const { id, label, type } = req;
      const list = await this.getConfigureList();
      if (!list) {
        throw new Error("组态列表不存在");
      }
      let duplicates = filter(list, { label: label });
      if (type == "project") {
        if (duplicates.length > 0) {
          throw new Error("名称重复，请重新输入");
        }
        // 设置id
        req.id = getUUID();
        list.push(req);
      } else if (type == "hmi") {
        const project = find(list, { id: id });
        if (!project) {
          throw new Error("项目不存在");
        }
        duplicates = filter(project.children, { label: label });
        if (duplicates.length > 0) {
          throw new Error("名称重复，请重新输入");
        }
        // 设置id
        req.id = getUUID();
        if (!project.children) {
          project.children = [];
        }
        project.children.push(req);
      }
      fs.writeFileSync(this.cfgPath, JSON.stringify(list));
      return list;
    } catch (error) {
      console.error("[ConfigureService] 新增组态异常:", error);
      throw error;
    }
  }

  // 重命名组态
  async renameConfigure(req: ConfigureInfo): Promise<ConfigureInfo[]> {
    try {
      const { id, pId, label, path, type } = req;
      const list = await this.getConfigureList();
      if (!list) {
        throw new Error("组态列表不存在");
      }
      let duplicates = filter(list, { label: label });
      let oldPath = "";
      if (type == "project") {
        if (duplicates.length > 0) {
          throw new Error("名称重复，请重新输入");
        }
        const project = find(list, { id: id });
        if (!project) {
          throw new Error("未找到项目");
        }
        if (!project.path) {
          throw new Error("未找到项目路径");
        }
        oldPath = project.path;
        project.label = label;
        project.path = path;
        if (project.children) {
          project.children.forEach((obj) => {
            if (!obj.path) {
              return;
            }
            const parts = obj.path.split("/");
            if (parts.length > 1) {
              const newPath = parts
                .map((part, index) => {
                  if (index === 1) {
                    return label; // 替换为新内容
                  }
                  return part;
                })
                .join("/");
              obj.path = newPath;
            }
          });
        }
      } else if (type == "hmi") {
        const parent = find(list, { id: pId });
        if (!parent) {
          throw new Error("项目未找到");
        }
        duplicates = filter(parent.children, { label: label });
        if (duplicates.length > 0) {
          throw new Error("名称重复，请重新输入");
        }
        const hmi = find(parent.children, { id: id });
        if (hmi) {
          if (hmi.path) {
            oldPath = hmi.path;
          }
          hmi.label = label;
          hmi.path = path;
        }
      } else {
        throw new Error("操作类型不正确，允许值[project,hmi]");
      }
      fs.writeFileSync(this.cfgPath, JSON.stringify(list));
      const oldFilePath = IECCONSTANTS.PATH_CONFIGURE + "/" + oldPath;
      const newFilePath = IECCONSTANTS.PATH_CONFIGURE + "/" + path;
      renameFolder(oldFilePath, newFilePath, type);
      return list;
    } catch (error) {
      console.error("[ConfigureService] 重命名组态异常:", error);
      throw error;
    }
  }

  // 删除组态
  async removeConfigure(req: ConfigureInfo): Promise<ConfigureInfo[]> {
    try {
      const { id, pId, path, type } = req;
      const list = await this.getConfigureList();
      if (!list) {
        throw new Error("组态列表不存在");
      }
      if (type == "project") {
        remove(list, (item) => item.id === id);
      } else if (type == "hmi") {
        const parent = find(list, { id: pId });
        if (parent && parent.children) {
          remove(parent.children, (item) => item.id === id);
        }
      }
      fs.writeFileSync(this.cfgPath, JSON.stringify(list));
      const removePath = IECCONSTANTS.PATH_CONFIGURE + "/" + path;
      deleteFolderRecursive(removePath);
      return list;
    } catch (error) {
      console.error("[ConfigureService] 获取组态列表异常:", error);
      throw error;
    }
  }

  // 保存组态
  async saveConfigure(req: SaveConfigureInfo): Promise<boolean> {
    try {
      const { path, label, data } = req;
      const savePath = IECCONSTANTS.PATH_CONFIGURE + "/" + path;
      if (!fs.existsSync(savePath)) {
        await fsPromise.mkdir(savePath, {
          recursive: true,
        });
      }
      fs.writeFileSync(savePath + "/" + label + ".json", data);
      return true;
    } catch (error) {
      console.error("[ConfigureService] 组态保存异常:", error);
      throw error;
    }
  }

  // 加载组态
  async loadConfigure(req: LoadConfigureInfo): Promise<any> {
    try {
      const { path, label } = req;
      const savePath =
        IECCONSTANTS.PATH_CONFIGURE + "/" + path + "/" + label + ".json";
      if (!fs.existsSync(savePath)) {
        return {};
      }
      const data = JSON.parse(fs.readFileSync(savePath, "utf8"));
      return data;
    } catch (error) {
      console.error(t("logs.configureService.loadConfigureError"), error);
      throw error;
    }
  }

  // 打开组态文件夹
  async openConfigureDir(req: openConfigureInfo): Promise<any> {
    try {
      const { path } = req;
      let dir = "";
      const currPath = nodePath.join(IECCONSTANTS.PATH_CONFIGURE, path);
      if (nodePath.isAbsolute(currPath)) {
        dir = currPath;
      } else {
        dir = electronApp.getPath(currPath as any);
      }
      if (!fs.existsSync(dir)) {
        throw new Error("路径不存在，请检查组态是否配置或保存");
      }
      await shell.openPath(dir);
    } catch (error) {
      console.error("[ConfigureService] 加载组态异常:", error);
      throw error;
    }
  }
}

const renameFolder = (oldPath: string, newPath: string, type: string) => {
  if (!fs.existsSync(oldPath)) {
    return;
  }
  if (type == "project") {
    fs.renameSync(oldPath, newPath);
    return;
  }
  fs.renameSync(oldPath, newPath);
  const oldJson = nodePath.join(newPath, nodePath.basename(oldPath) + ".json");
  const newJson = nodePath.join(newPath, nodePath.basename(newPath) + ".json");
  fs.renameSync(oldJson, newJson);
};
ConfigureService.toString = () => "[class ConfigureService]";
const configureService = new ConfigureService();

export { ConfigureService, configureService };
