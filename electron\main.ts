import { ElectronEgg } from "ee-core";
import { Lifecycle } from "./preload/lifecycle";
import { preload } from "./preload";
import { app, BrowserWindow } from "electron";
import { initI18n } from "./data/i18n/i18n";

// Initialize i18n system
initI18n();

// New app
const electronApp = new ElectronEgg();

// 设置内容安全策略
app.on("web-contents-created", (event, contents) => {
  contents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        "Content-Security-Policy": [
          "default-src 'self';",
          "script-src 'self' 'unsafe-inline';",
          "style-src 'self' 'unsafe-inline';",
          "img-src 'self' data: https:;",
          "connect-src 'self' https:;",
          "font-src 'self' data:;",
          "object-src 'none';",
          "base-uri 'self';",
          "form-action 'self';",
          "frame-ancestors 'none';",
          "block-all-mixed-content;",
          "upgrade-insecure-requests;",
        ].join(" "),
      },
    });
  });
});

// Register lifecycle
const life = new Lifecycle();
electronApp.register("ready", life.ready);
electronApp.register("electron-app-ready", life.electronAppReady);
electronApp.register("window-ready", life.windowReady);
electronApp.register("before-close", life.beforeClose);
// Register preload
electronApp.register("preload", preload);

// Run
electronApp.run();
