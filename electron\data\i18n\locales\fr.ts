/**
 * French language pack
 */
export default {
  // Error messages
  errors: {
    success: "Succès",
    deviceNotConnected: "Appareil non connecté",
    invalidParam: "Paramètre invalide",
    operateFailed: "Opération échouée",
    noData: "Aucune donnée",
    internalError: "Erreur interne",
    connectionExists: "La connexion existe déjà",
    fileContentEmpty: "Le contenu du fichier est vide",
    deviceNotConnectedOrDisconnected: "Appareil non connecté ou déconnecté.",
    getServiceErrorInfo: "Impossible d'obtenir les informations d'erreur correspondantes",
    saveReportFileError: "Erreur lors de la sauvegarde du fichier de rapport rpt",
    getConfigureListError: "Erreur lors de l'obtention de la liste de configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration",
    cancelUploadError: "Erreur lors de l'annulation du téléchargement du fichier d'onde",
    openWaveFileError: "Erreur lors de l'ouverture du fichier d'onde",
    getFileDataSuccess: "Contenu des données du fichier récupéré avec succès !"
  },
  
  // Log messages
  logs: {
    reportController: {
      getCommonReportListEntry: "getCommonReportList paramètres d'entrée",
      getCommonReportListReturn: "getCommonReportList retour",
      getCommonReportListError: "getCommonReportList erreur",
      cancelUploadStart: "Annulation du téléchargement du fichier d'onde démarrée",
      cancelUploadError: "Erreur d'annulation du téléchargement du fichier d'onde",
      openWaveFileStart: "Ouverture du fichier d'onde démarrée",
      openWaveFileError: "Erreur d'ouverture du fichier d'onde"
    },
    configureService: {
      getConfigureListError: "Erreur d'obtention de la liste de configuration",
      loadConfigureError: "Erreur de chargement de la configuration"
    },
    paramService: {
      getDiffParamComplete: "Comparaison terminée, nombre de groupes de différences",
      getAllDiffParamError: "getAllDiffParam erreur"
    }
  },
  
  // Common messages
  common: {
    start: "Démarré",
    stop: "Arrêté",
    other: "Autre",
    loading: "Chargement...",
    success: "Succès",
    failed: "Échoué",
    cancel: "Annuler",
    confirm: "Confirmer"
  }
};
