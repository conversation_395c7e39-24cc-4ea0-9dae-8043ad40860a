import { logger } from "ee-core/log";
import { ExcelExporter } from "../../utils/excelUtils"; // 引入ExcelExporter
import { Column } from "../../interface/debug/exportTypes";
import { IECReq } from "../../interface/debug/request";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import path from "node:path";
import { WordUtils } from "../../utils/wordUtils";
import IECCONSTANTS from "../../data/debug/iecConstants";
import { DebugInfoItem, MenuIdName } from "../../interface/debug/debuginfo";
import { debugInfoMenuService } from "./debuginfomenu";
import { DataValueReadRequestData } from "iec-upadrpc/dist/src/data";
import { MENU_ITEM } from "../../data/debug/menuItem";
import { t } from "../../data/i18n/i18n";

/**
 * 装置信息Service
 * <AUTHOR>
 * @class
 */
class DeviceInfoService {
  // Excel导出
  private excelExporter: ExcelExporter;

  constructor() {
    this.excelExporter = new ExcelExporter();
  }

  // 异步获取装置信息，返回DeviceinfoItem列表
  async getDeviceInfo(req: IECReq<any>): Promise<DebugInfoItem[]> {
    logger.info("[DeviceInfoService] getDeviceInfo 入参:", JSON.stringify(req));
    try {
      logger.debug("[DeviceInfoService] getDeviceInfo - 获取设备信息");
      const device = GlobalDeviceData.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const { names } = req.data;
      const param: MenuIdName = {
        id: req.head.id,
        type: "submenu",
        names: [names[0], names[1]],
      };
      logger.info("param：", param);
      const items: DebugInfoItem[] =
        debugInfoMenuService.getTreeItemByName(param);
      const resultItem: DebugInfoItem[] = [];
      if (names[1] === MENU_ITEM.DEV_BASIC_TABLE) {
        const result = await client?.getDeviceVersion([]);
        if (result?.isSuccess()) {
          logger.info(result);
          for (const key in result.data) {
            const value = result.data[key];
            if (typeof value === "string") {
              resultItem.push({
                name: key,
                desc: key,
                value: value,
              });
            } else if (typeof value === "object") {
              resultItem.push({
                name: key,
                desc: key,
                value: JSON.stringify(value),
              });
            }
          }
          // device?.deviceClient.getDeviceVersion();
          logger.info(
            "[DeviceInfoService] getDeviceInfo 返回:",
            resultItem.length > 0 ? resultItem : items
          );
          return resultItem.length > 0 ? resultItem : items;
        }
        // 获取业务错误信息
        throw new Error(String(result?.msg));
      } else {
        const vkeys = items.map((item) => (item.vKey ? item.vKey : ""));
        const dataRequest: DataValueReadRequestData = {
          infItems: vkeys,
          batchSize: IECCONSTANTS.BATCH_COUNT,
        };
        const result = await client?.getDataValues(dataRequest);
        if (result?.isSuccess()) {
          logger.info(
            t("common.requestDataSize"),
            items.length,
            t("common.responseDataSize"),
            result.data.value.length,
            "\n"
          );
          if (items.length != result.data.value.length) {
            const msg = t("errors.dataLengthMismatch");
            throw new Error(msg);
          }
          logger.info("result:", result);
          for (let i = 0; i < result.data.value.length; i++) {
            // logger.info(items[i]);
            items[i].value = result.data.value[i]?.value;
          }
          logger.info("[DeviceInfoService] getDeviceInfo 返回:", items);
          return items;
        }
        // 获取业务错误信息
        throw new Error(String(result?.msg));
      }
    } catch (error) {
      logger.error("[DeviceInfoService] getDeviceInfo 异常:", error);
      throw error;
    }
  }

  // 导出装置基本信息
  async exportDeviceInfo(req: IECReq<any>) {
    logger.info("[DeviceInfoService] exportDeviceInfo 入参:", req);
    try {
      logger.debug("[DeviceInfoService] exportDeviceInfo - 开始导出");
      const { data, selectPath } = req.data;
      // 定义列
      const columns: Column[] = [
        {
          header: t("params.headers.description"),
          key: "description",
          width: 30,
        },
        { header: t("params.headers.value"), key: "value", width: 30 },
      ];
      if (path.extname(selectPath).toLowerCase() === ".xlsx") {
        await this.excelExporter.exportToExcel(
          data,
          columns,
          selectPath,
          t("deviceInfo.basicInfo")
        );
        logger.info("[DeviceInfoService] exportDeviceInfo 导出成功");
        return true;
      } else if (path.extname(selectPath).toLowerCase() === ".docx") {
        let fileData;
        if (data.length > 0) {
          const deviceInfos: any[] = [];
          let i = 1;
          for (const item of data) {
            const info = {
              index: i,
              desc: item.description,
              value: item.value,
            };
            deviceInfos.push(info);
            i++;
          }
          fileData = { deviceInfos: deviceInfos };
        }
        await WordUtils.buildWordFile(
          IECCONSTANTS.PATH_TEMPLATE + "/deviceInfoTemplate.docx",
          fileData,
          selectPath
        );
        logger.info("[DeviceInfoService] exportDeviceInfo 导出成功");
        return true;
      }
      return false;
    } catch (error) {
      logger.error("[DeviceInfoService] exportDeviceInfo failed", { error });
      throw error;
    }
  }
}

DeviceInfoService.toString = () => "[class DeviceInfoService]";
const deviceInfoService = new DeviceInfoService();

export { DeviceInfoService, deviceInfoService };
