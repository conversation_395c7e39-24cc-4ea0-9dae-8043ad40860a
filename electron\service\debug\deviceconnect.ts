"use strict";

import { UpadRpcConnectOptions, UpadRpcConnectRes } from "iec-upadrpc/dist/src/upadrpc/UpadRpcDef";
import { DebugDeviceInfo } from "../../interface/debug/debuginfo";

import { UPAD_RPC_DISCONNECT } from "iec-upadrpc/dist//src/UpadRpcConstants";

import { UpadRcpClient } from "iec-upadrpc/dist/src/UpadRpcClient";
import { IECResult } from "iec-common/dist/data/iecdata";
import { SingleGlobalDeviceInfo } from "../../data/debug/singleGlobalDeviceInfo";
import IECCONSTANTS from "../../data/debug/iecConstants";
import GlobalDeviceData from "../../data/debug/globalDeviceData";
import { IECReq } from "../../interface/debug/request";
import { IEC_EVENT, IECNotify } from "../../data/debug/notify";
import { sendMessageToUIByNotify } from "../../utils/iecUiUtils";
import { getMainWindow } from "ee-core/electron";
import { debugInfoMenuService } from "./debuginfomenu";
import { XmlFileManager } from "../../utils/xmlFileManager";
import { t } from "../../data/i18n/i18n";

const { logger } = require("ee-core/log");

/**
 * 装置连接Service
 * <AUTHOR>
 * @class
 */
class DeviceConnectService {
  // 检查装置是否连接，连接返回true，未连接返回false
  async isConnected(id: string): Promise<boolean> {
    logger.info("[DeviceConnectService] isConnected 入参:", id);
    try {
      const client = GlobalDeviceData.getInstance().getClient(id);
      if (client) {
        logger.debug(
          `[DeviceConnectService] isConnected - 客户端已获取，设备ID: ${id}`
        );
        return client.isConnected();
      }
      logger.warn(
        `[DeviceConnectService] isConnected - 未获取到客户端，设备ID: ${id}`
      );
      return false;
    } catch (error) {
      logger.error(`[DeviceConnectService] isConnected 异常:`, error);
      return false;
    }
  }

  // 检查装置是否连接
  async checkConnection(req: IECReq<any>): Promise<boolean> {
    logger.info(
      "[DeviceConnectService] checkConnection 入参:",
      JSON.stringify(req)
    );
    try {
      const connected = await deviceConnectService.isConnected(req.head.id);
      if (!connected) {
        logger.warn("[DeviceConnectService] Device is not connected");
      } else {
        logger.debug(
          `[DeviceConnectService] checkConnection - 设备已连接，设备ID: ${req.head.id}`
        );
      }
      return connected;
    } catch (error) {
      logger.error("[DeviceConnectService] checkConnection 异常:", error);
      return false;
    }
  }

  /**
   * 连接装置 */
  async connectDeviceByRpc(
    dataStr: string
  ): Promise<IECResult<UpadRpcConnectRes>> {
    logger.info("[DeviceConnectService] connectDeviceByRpc 入参:", dataStr);
    const globalDeviceData = GlobalDeviceData.getInstance();
    const data: DebugDeviceInfo = JSON.parse(dataStr);
    // 判断是否存在错误码信息,首次连接时如果连接出错，提供默认错误码，但是
    let singleGlobalDeviceInfo = globalDeviceData.getDeviceInfoGlobal(data.id);
    if (!singleGlobalDeviceInfo) {
      singleGlobalDeviceInfo = new SingleGlobalDeviceInfo(data.id);
      await singleGlobalDeviceInfo.initEnumTypeAndFile();
    }
    const enumType = singleGlobalDeviceInfo.enumTypes.get("ServiceError");
    // 调取接口
    const connectOptios: UpadRpcConnectOptions = {
      ip: data.ip,
      port: Number(data.port),
      connectTimeout: Number(data.connectTimeout),
      readTimeout: Number(data.readTimeout),
      libPath: IECCONSTANTS.PATH_DLL + "\\upadrpc",
    };
    logger.info("connectOptios:", connectOptios);
    // 使用装置的id作为key
    const key = data.id;
    // 从全局变量中获取连接对象
    let upadRcpClient = globalDeviceData.deviceInfoMap.get(key)?.deviceClient;
    if (upadRcpClient && upadRcpClient.isConnected()) {
      await upadRcpClient.disconnect();
    }
    upadRcpClient = new UpadRcpClient();
    /*
    upadRcpClient.on("upadRpcMessage", (data) => {
      console.log(data);
    });
    */

    upadRcpClient.on(UPAD_RPC_DISCONNECT, (data) => {
      // 通知前端修改连接状态
      const notify: IECNotify = {
        type: "disconnect",
        data: null,
      };
      sendMessageToUIByNotify(IEC_EVENT.NOTIFY, notify, getMainWindow());
    });

    let connResult;
    for (let i = 0; i < 3; i++) {
      connResult = await upadRcpClient.connect(connectOptios);
      if (connResult.isSuccess()) {
        break;
      } else {
        logger.warn(`第${i + 1}次连接失败:`, connResult);
        // 可选：每次重试间隔一段时间
        if (i < 2) {
          await new Promise((res) => setTimeout(res, 500));
        }
      }
    }
    if (!connResult.isSuccess()) {
      let msg = enumType?.get(connResult.data?.code + "");
      if (!msg) {
        msg = t("errors.connectionFailed");
      }
      if (connResult.code === 10) {
        msg = t("errors.connectionTimeout");
      }
      connResult.msg = msg;
      return connResult;
    }
    logger.info(t("common.connectionSuccess"), connResult);
    // 将连接成功的连接对象缓存
    let realSingleGlobalDeviceInfo = globalDeviceData.getDeviceInfoGlobal(
      data.id
    );
    if (!realSingleGlobalDeviceInfo) {
      realSingleGlobalDeviceInfo = new SingleGlobalDeviceInfo(data.id);
    }
    // 复位数据
    realSingleGlobalDeviceInfo.resetData();
    realSingleGlobalDeviceInfo.deviceClient = upadRcpClient;
    // 比较md5码，然后设置文件位置
    const xmlFileManager = new XmlFileManager();
    await xmlFileManager.setDebugInfoFilePath(
      key,
      upadRcpClient,
      realSingleGlobalDeviceInfo
    );
    // 往realSingleGlobalDeviceInfo添加debugInfo 和debugItemMap
    globalDeviceData.setDeviceInfoGlobal(key, realSingleGlobalDeviceInfo);
    realSingleGlobalDeviceInfo.debugInfo =
      await debugInfoMenuService.getDebugInfo(key);
    logger.debug(
      `[DeviceConnectService] connectDeviceByRpc - 连接成功，设备ID: ${data.id}`
    );
    return connResult;
  }

  // 断开连接
  async disconnectDevice(deviceId: string): Promise<any> {
    logger.info("[DeviceConnectService] disconnectDevice 入参:", deviceId);
    try {
      // 获取连接
      const deviceInfoGlobal =
        GlobalDeviceData.getInstance().getDeviceInfoGlobal(deviceId);
      let upadRcpClient = deviceInfoGlobal?.deviceClient;
      if (upadRcpClient && typeof UpadRcpClient) {
        const result = await upadRcpClient.disconnect();
        if (result) {
          // 删除连接对象
          upadRcpClient = undefined;
        }
        logger.info(
          `[DeviceConnectService] disconnectDevice - 断开连接成功，设备ID: ${deviceId}`
        );
        return result;
      }
    } catch (error) {
      logger.error(`[DeviceConnectService] disconnectDevice 异常:`, error);
      throw error;
    }
  }
}

DeviceConnectService.toString = () => "[class DeviceConnectService]";
const deviceConnectService = new DeviceConnectService();

export { DeviceConnectService, deviceConnectService };
