import { logger } from "ee-core/log";
import { ApiPagination } from "../../data/debug/apiPagenation";
import VariableItem from "../../data/debug/variableItem"; // 确保正确导入
import { ExcelExporter } from "../../utils/excelUtils"; // 引入ExcelExporter // 引入ExcelParser
import { IECReq } from "../../interface/debug/request";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
import { removeLastCarriage } from "../../utils/common";
import { Column } from "../../interface/debug/exportTypes";
import { t } from "../../data/i18n/i18n";

/**
 * 参数定值Service
 * <AUTHOR>
 * @class
 */
class VariableService {
  // Excel导出工具
  private excelExporter: ExcelExporter;

  constructor() {
    this.excelExporter = new ExcelExporter();
  }

  // 异步获取装置变量定值，返回VariableItem列表
  async getVariable(req: IECReq<any>): Promise<ApiPagination<VariableItem>> {
    // 获取装置变量方法入口日志
    logger.info("[VariableService] getVariable input:", JSON.stringify(req));
    try {
      logger.info("[VariableService] getVariable");
      // 从params取出分页参数pageNum，pageSize参数
      const { pageNum, pageSize, name } = req.data;
      const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = devInfo?.deviceClient;
      const itemMap = devInfo?.debugItemMap;
      // 按照注册顺序排序
      const variableItems = devInfo?.variableItems || [];
      const orderIndexMap = new Map();
      for (let index = 0; index < variableItems.length; index++) {
        const element = variableItems[index];
        orderIndexMap.set(element, index);
      }
      // 获取装置变量定值并返回，构造数据
      const varInfos = await client?.getVarInfos();

      logger.info("varInfos:", varInfos);
      const items: VariableItem[] = [];
      varInfos?.forEach((value, key) => {
        return items.push({
          index: orderIndexMap.get(value.name),
          id: key,
          desc: itemMap?.get(value.name) || "",
          name: value.name,
          type: value.type,
          addr: "",
          value: String(value.value) || "",
        });
      });

      // 使用 sort 方法对 arrayToSort 进行排序
      items.reverse();
      // 增加序号
      items.forEach((item, key) => {
        item.index = key + 1; // 从 1 开始递增
      });
      // 根据 name 进行过滤
      const filteredData = name
        ? items?.filter((item) => item.name.includes(name))
        : items;

      // 模拟变量数据，从中获取指定pageNum，和pageSize数据
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const result = filteredData.slice(startIndex, endIndex);

      // 创建 ApiPagination 对象
      const apiPagination = new ApiPagination<VariableItem>(
        filteredData.length,
        pageSize,
        pageNum,
        result
      );
      logger.info(
        "[VariableService] getVariable return:",
        JSON.stringify(apiPagination)
      );
      return apiPagination;
    } catch (error) {
      logger.error("[VariableService] getVariable error:", error);
      throw error;
    }
  }

  // 异步添加装置变量，成功返回true，失败返回false
  async addVariable(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info("[VariableService] addVariable", req);
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;

      // 注册单个变量
      const param = req.data;
      const variables = device?.variableItems;
      const existingVariable = variables?.find((item) => item === param.name);
      if (existingVariable) {
        throw new Error(`变量名称${param.name}已经存在！`);
      }
      const result = await client?.varSubscribe([param.name]);
      if (result?.isSuccess()) {
        if (variables) {
          result.data.forEach((item) => {
            // 在这里执行业务逻辑
            if (item.error == 0) {
              variables.unshift(param.name); // 将新数据插入到数组的开头
              device?.setVaribaleItem(variables);
            } else {
              // 注册变量失败
              throw new Error(
                item.name +
                  t("variable.registerFailed") +
                  device?.getServiceErrMsgByCode(String(item.error))
              );
            }
          });
        }
        logger.info("[VariableService] addVariable result", true);
        return true;
      }
      throw new Error(String(result?.msg));
    } catch (error) {
      logger.error("[VariableService] addVariable error", { error });
      throw error;
    }
  }

  // 异步修改装置变量信息，成功返回true，失败返回false
  async modifyVariable(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info("[VariableService] modifyVariable", req.data);
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;

      const result = await client?.varWrite(req.data);
      if (result?.isSuccess()) {
        if (result.data.success) {
          logger.info("[VariableService] modifyVariable result", true);
          return true;
        } else {
          let msg;
          const resultData = result.data.errorData || [];
          resultData.forEach((item) => {
            // 在这里执行业务逻辑
            msg +=
              item.name +
              t("variable.modifyFailed") +
              device?.getServiceErrMsgByCode(String(result.data.serviceError)) +
              "\n";
          });
          throw new Error(removeLastCarriage(msg));
        }
      }
      throw new Error(String(result?.msg));
    } catch (error) {
      logger.error("[VariableService] modifyVariable error", error);
      throw new Error(t("variable.modifyError"));
    }
  }

  // 异步删除装置变量信息，成功返回true，失败返回false
  async deleteVariable(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info("[VariableService] deleteVariable", { req });
      const param = req.data;
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const result = await client?.varUnsubscribe({
        all: false,
        name: param,
      });
      if (result?.isSuccess()) {
        const variables = device?.variableItems;
        const variableToDeleteFailed = result.data.errorData?.map(
          (vn) => vn.name
        );

        // 删除失败的移除
        const realDelete = param?.filter(
          (item) => !variableToDeleteFailed?.includes(item)
        );
        const updatedVariables = variables?.filter(
          (item) => !realDelete.includes(item)
        );
        if (updatedVariables) {
          device?.setVaribaleItem(updatedVariables);
        }

        if (!result.data.success) {
          let msg;
          const resultData = result.data.errorData || [];
          resultData.forEach((item) => {
            // 在这里执行业务逻辑
            msg +=
              item.name +
              "删除变量失败，失败原因：" +
              device?.getServiceErrMsgByCode(String(item.error)) +
              "\n";
          });
          throw new Error(removeLastCarriage(msg));
        }
        logger.info("[VariableService] deleteVariable result", true);
        return true;
      }
      throw new Error(String(result?.msg));
    } catch (error) {
      logger.error("[VariableService] deleteVariable error", error);
      throw new Error("变量删除失败");
    }
  }

  // 异步导入装置变量信息，成功返回true，失败返回false
  async importVariable(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info("[VariableService] importVariable", req);
      const { path } = req.data;

      logger.info("path:", path);

      // 定义 keyMapping
      const keyMapping = {
        变量名称: "name",
      };

      const parsedData = await this.excelExporter.parseExcel(path, keyMapping);
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;
      const variables = device?.variableItems;
      let msg = "";
      const excelData: any[] = [];
      logger.info("parsedData:", parsedData);
      if (variables) {
        parsedData.forEach((item) => {
          const existingVariable = variables.find((v) => v === item.name);
          if (existingVariable) {
            msg += `变量名称${item.name}已经存在！\n`;
          } else {
            excelData.push(item);
          }
        });
      }
      logger.info("parsedData:", excelData);
      const variableNamesToAdd = excelData.map((vn) => vn.name);
      logger.info("variableNamesToAdd:", variableNamesToAdd);
      const result = await client?.varSubscribe(variableNamesToAdd);

      logger.info("result:", result);

      if (result?.isSuccess()) {
        if (variables) {
          logger.info("variables:", variables);
          result.data.forEach((item) => {
            // 在这里执行业务逻辑
            if (item.error == 0) {
              variables.unshift(item.name); // 将新数据插入到数组的开头
            } else {
              msg +=
                item.name +
                "注册变量失败，失败原因：" +
                device?.getServiceErrMsgByCode(String(item.error) + "\n");
            }
          });
          device?.setVaribaleItem(variables);
        }
        if (msg.length > 0) {
          throw Error(removeLastCarriage(msg));
        }
        logger.info("[VariableService] addVariable result", true);
        return true;
      }
      throw new Error(String(result?.msg));
    } catch (error) {
      logger.error("[VariableService] importVariable error", { error });
      throw error;
    }
  }

  // 导出变量
  async exportVariable(req: IECReq<any>): Promise<boolean> {
    const columns: Column[] = [
      { header: t("variable.headers.index"), key: "index", width: 20 },
      { header: t("variable.headers.name"), key: "name", width: 40 },
      { header: t("variable.headers.description"), key: "desc", width: 40 },
      { header: t("variable.headers.type"), key: "type", width: 20 },
      { header: t("variable.headers.value"), key: "value", width: 20 },
    ];
    const devInfo = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
      req.head.id
    );
    const client = devInfo?.deviceClient;
    // 按照注册顺序排序
    const variableItems = devInfo?.variableItems || [];
    const itemMap = devInfo?.debugItemMap;
    const orderIndexMap = new Map();
    for (let index = 0; index < variableItems.length; index++) {
      const element = variableItems[index];
      orderIndexMap.set(element, index);
    }
    const varInfos = await client?.getVarInfos();

    logger.info("varInfos:", varInfos);
    const items: VariableItem[] = [];
    varInfos?.forEach((value, key) => {
      return items.push({
        index: orderIndexMap.get(value.name),
        id: key,
        desc: itemMap?.get(value.name) || "",
        name: value.name,
        type: value.type,
        addr: "",
        value: String(value.value),
      });
    });
    // 增加序号
    items.forEach((item, key) => {
      item.index = key + 1; // 从 1 开始递增
    });

    let data = items.map((item, index) => ({
      index: index + 1,
      name: item.name,
      desc: item.desc,
      type: item.type,
      value: item.value,
    }));
    const { path } = req.data;
    logger.info("export path : ", path);
    try {
      if (!data) data = [];
      await this.excelExporter.exportToExcel(
        data,
        columns,
        path,
        "        t("variable.debugVariables")"
      );
      return true;
    } catch (error) {
      logger.error("[variable] exportVariable failed", { error });
      throw new Error("Failed to export variable info");
    }
  }
}

VariableService.toString = () => "[class VariableService]";
const variableService = new VariableService();

export { VariableService, variableService };
