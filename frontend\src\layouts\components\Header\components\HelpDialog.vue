<template>
  <el-dialog class="help-main" v-model="dialogVisible" :title="''" width="1100px" draggable center>
    <div class="about-header">
      <img class="about-logo" :src="logoSrc" alt="logo" />
      <div class="about-title-group">
        <span class="about-title">VisualDebug</span>
        <span class="about-subtitle">{{ t("layout.header.moreInfo.items.help") }}</span>
      </div>
    </div>
    <el-divider class="help-divider" />
    <div class="help-container" style="height: 60vh">
      <!-- 左侧目录导航 -->
      <div class="help-sidebar" style="padding: 3px">
        <div class="sidebar-header">
          <el-icon><Document /></el-icon>
          <span>{{ t("help.catalog") }}</span>
        </div>
        <el-input v-model="searchText" :placeholder="t('help.searchPlaceholder')" clearable size="small" />
        <div class="sidebar-content">
          <el-tree
            ref="treeRef"
            :data="filteredTocData"
            :props="treeProps"
            node-key="id"
            @node-click="handleNodeClick"
            :current-node-key="currentNodeKey"
            :highlight-current="true"
            :key="treeKey + searchText"
            class="toc-tree"
          >
            <template #default="{ data }">
              <span class="toc-node">
                <el-icon v-if="data.level === 1" class="toc-icon">
                  <Document />
                </el-icon>
                <el-icon v-else-if="data.level === 2" class="toc-icon">
                  <QuestionFilled />
                </el-icon>
                <el-icon v-else class="toc-icon">
                  <Setting />
                </el-icon>
                <span class="toc-text">{{ data.label }}</span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="help-content-area">
        <div class="content-header">
          <el-icon class="help-icon"><QuestionFilled /></el-icon>
          <span class="help-title">{{ currentSection.title || t("layout.header.moreInfo.items.help") }}</span>
        </div>
        <el-divider class="help-divider" />
        <div class="content-body">
          <div v-if="currentSection.content" class="help-content" v-html="highlightKeyword(currentSection.content, searchText)" />
          <div v-else class="help-content" v-html="highlightKeyword(helpHtml, searchText)" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, computed, watch } from "vue";
import { useI18n } from "vue-i18n";
import { QuestionFilled, Document, Setting } from "@element-plus/icons-vue";
import MarkdownIt from "markdown-it";
import logo from "@/assets/images/logo.png";

const { t } = useI18n();
const dialogVisible = ref(false);
const helpHtml = ref("");
const currentSection = reactive({
  title: "",
  content: ""
});
const logoSrc = logo;

const md = new MarkdownIt();

// 目录数据结构
const tocData = ref([] as any[]);
const treeKey = ref(0);
const treeRef = ref();
const currentNodeKey = ref("");
const searchText = ref("");

// 递归过滤目录树，保留匹配节点及其父节点
const filterToc = (items: any[], keyword: string): any[] => {
  if (!keyword) return items;
  const result: any[] = [];
  for (const item of items) {
    const matched = item.label.includes(keyword) || (item.content && item.content.includes(keyword));
    let children: any[] = [];
    if (item.children) {
      children = filterToc(item.children, keyword);
    }
    if (matched || children.length) {
      result.push({ ...item, children });
    }
  }
  return result;
};

const filteredTocData = computed(() => filterToc(tocData.value, searchText.value));

// 内容高亮关键词
function highlightKeyword(html, keyword) {
  if (!keyword) return html;
  return html.replace(
    new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "gi"),
    match => `<mark style="background: #ffe58f; color: #d48806">${match}</mark>`
  );
}

const treeProps = {
  children: "children",
  label: "label"
};

// 解析markdown内容，提取目录结构
const parseMarkdownToc = (markdownText: string) => {
  // console.log("[parseMarkdownToc] 输入markdownText:", markdownText);
  const lines = markdownText.split(/\r?\n/);
  const toc: any[] = [];
  const sections: { [key: string]: string } = {};

  let currentSection = "";
  let currentContent = "";

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
    if (headingMatch) {
      if (currentSection && currentContent.trim()) {
        sections[currentSection] = currentContent.trim();
        // console.log(`[parseMarkdownToc] 收集section:`, currentSection, currentContent.trim());
      }
      const level = headingMatch[1].length;
      const title = headingMatch[2].trim();
      const id = title.toLowerCase().replace(/[^a-z0-9\u4e00-\u9fa5]/g, "-");
      const tocItem = {
        id,
        label: title,
        icon: level === 1 ? "Document" : level === 2 ? "QuestionFilled" : "Setting",
        content: "",
        level
      };
      // console.log(`[parseMarkdownToc] 发现标题:`, { level, title, id });
      if (level === 1) {
        toc.push(tocItem);
      } else if (level === 2) {
        if (toc.length > 0) {
          if (!toc[toc.length - 1].children) {
            toc[toc.length - 1].children = [];
          }
          toc[toc.length - 1].children.push(tocItem);
        } else {
          toc.push(tocItem);
        }
      } else {
        let parent = toc;
        for (let j = 0; j < level - 2; j++) {
          if (parent.length > 0 && parent[parent.length - 1].children) {
            parent = parent[parent.length - 1].children;
          } else {
            break;
          }
        }
        if (parent.length > 0) {
          if (!parent[parent.length - 1].children) {
            parent[parent.length - 1].children = [];
          }
          parent[parent.length - 1].children.push(tocItem);
        }
      }
      currentSection = id;
      currentContent = "";
    } else {
      if (currentSection) {
        currentContent += line + "\n";
      }
    }
  }
  if (currentSection && currentContent.trim()) {
    sections[currentSection] = currentContent.trim();
    // console.log(`[parseMarkdownToc] 收集section:`, currentSection, currentContent.trim());
  }
  const setContent = (items: any[]) => {
    items.forEach(item => {
      if (sections[item.id]) {
        item.content = md.render(sections[item.id]);
        // console.log(`[parseMarkdownToc] 设置content:`, item.id, sections[item.id]);
      }
      if (item.children) {
        setContent(item.children);
      }
    });
  };
  setContent(toc);
  // console.log("[parseMarkdownToc] toc结构:", toc);
  return toc;
};

// 只收集一级菜单id
const collectExpandedIds = (items: any[]): string[] => {
  return items.map(item => item.id);
};

// 处理目录节点点击
const handleNodeClick = (data: any) => {
  if (data.content) {
    currentSection.title = data.label;
    currentSection.content = data.content;
  }
};

const expandNodes = (ids: string[]) => {
  if (treeRef.value && treeRef.value.store && treeRef.value.store.nodesMap) {
    ids.forEach(id => {
      const node = treeRef.value.store.nodesMap[id];
      if (node) node.expanded = true;
    });
  }
};

const findFirstContentNode = (items: any[]) => {
  for (const item of items) {
    if (item.content) return item;
    if (item.children) {
      const found = findFirstContentNode(item.children);
      if (found) return found;
    }
  }
  return null;
};

// 获取当前语言对应的帮助文档文件名
const getHelpFileName = () => {
  const { locale } = useI18n();
  const languageFileMap = {
    zh: "help.md",
    en: "help-en.md",
    es: "help-es.md",
    fr: "help-fr.md"
  };
  return languageFileMap[locale.value] || "help.md";
};

const loadHelpMd = async () => {
  try {
    const helpFileName = getHelpFileName();
    const res = await fetch(`${import.meta.env.BASE_URL}${helpFileName}`);

    // 如果当前语言的帮助文档不存在，回退到中文版本
    if (!res.ok) {
      console.warn(`帮助文档 ${helpFileName} 不存在，回退到中文版本`);
      const fallbackRes = await fetch(`${import.meta.env.BASE_URL}help.md`);
      const text = await fallbackRes.text();
      helpHtml.value = md.render(text);
      const toc = parseMarkdownToc(text);
      tocData.value = toc;
    } else {
      const text = await res.text();
      helpHtml.value = md.render(text);
      const toc = parseMarkdownToc(text);
      tocData.value = toc;
    }

    treeKey.value++;
    await nextTick();
    const ids = collectExpandedIds(tocData.value);
    expandNodes(ids);
  } catch (error) {
    console.error("加载帮助文档失败:", error);
    helpHtml.value = "<p>" + t("help.loadFail") + "</p>";
  }
};

const openDialog = async () => {
  dialogVisible.value = true;
  await loadHelpMd();
  // 默认展开并高亮第一个有内容的章节
  const firstNode = findFirstContentNode(tocData.value);
  if (firstNode) {
    currentNodeKey.value = firstNode.id;
    await nextTick();
    handleNodeClick(firstNode);
    // 自动滚动到高亮节点
    if (treeRef.value) treeRef.value.setCurrentKey(firstNode.id);
  }
};

// 监听语言变化，自动重新加载帮助文档
const { locale } = useI18n();
watch(locale, async (newLocale, oldLocale) => {
  if (newLocale !== oldLocale && dialogVisible.value) {
    console.log(`语言从 ${oldLocale} 切换到 ${newLocale}，重新加载帮助文档`);
    await loadHelpMd();
    // 重新展开并高亮第一个有内容的章节
    const firstNode = findFirstContentNode(tocData.value);
    if (firstNode) {
      currentNodeKey.value = firstNode.id;
      await nextTick();
      handleNodeClick(firstNode);
      if (treeRef.value) treeRef.value.setCurrentKey(firstNode.id);
    }
  }
});

defineExpose({ openDialog });
</script>

<style lang="scss">
.help-main {
  .el-dialog__header {
    padding: 0;
    border-bottom: none;
  }
  .el-dialog__body {
    max-height: 80vh;
    padding: 0;
    overflow: auto;
    background: #fafbfc;
    [class="dark"] & {
      background: #23272e;
    }
  }
  .about-header {
    display: flex;
    gap: 18px;
    align-items: center;
    margin-bottom: 8px;
    .about-logo {
      width: 48px;
      height: 48px;
      object-fit: contain;
      background: #ffffff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
      [class="dark"] & {
        background: #23272e;
        box-shadow: 0 2px 8px rgb(0 0 0 / 40%);
      }
    }
    .about-title-group {
      display: flex;
      flex-direction: column;
      .about-title {
        font-size: 20px;
        font-weight: bold;
        color: var(--el-color-primary);
        letter-spacing: 1px;
        [class="dark"] & {
          color: #8cd2ff;
        }
      }
      .about-subtitle {
        margin-top: 2px;
        font-size: 15px;
        color: var(--el-text-color-secondary);
        [class="dark"] & {
          color: #b0b8c7;
        }
      }
    }
  }
  .help-container {
    display: flex;
    height: 60vh;
    min-height: 400px;
    max-height: 70vh;
    .help-sidebar {
      width: 280px;
      max-height: 68vh;
      overflow-y: hidden;
      background: #ffffff;
      border-right: 1px solid var(--el-border-color-light);
      [class="dark"] & {
        background: #23272e;
        border-right: 1px solid #363b40;
      }
      .sidebar-header {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 16px 20px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        border-bottom: 1px solid var(--el-border-color-light);
        .el-icon {
          color: var(--el-color-primary);
        }
        [class="dark"] & {
          color: #b0b8c7;
          border-bottom: 1px solid #363b40;
        }
      }
      .sidebar-content {
        height: calc(100% - 60px);
        max-height: 56vh;
        padding: 12px 0;
        overflow-y: auto;
        .toc-tree {
          .toc-node {
            display: flex;
            gap: 8px;
            align-items: center;
            padding: 4px 0;
            .toc-icon {
              font-size: 14px;
              color: var(--el-color-primary);
              [class="dark"] & {
                color: #8cd2ff;
              }
            }
            .toc-text {
              font-size: 14px;
              [class="dark"] & {
                color: #b0b8c7;
              }
            }
          }
        }
      }
    }
    .help-content-area {
      display: flex;
      flex: 1;
      flex-direction: column;
      .content-header {
        display: flex;
        gap: 12px;
        align-items: center;
        padding: 20px 24px 0;
        .help-icon {
          font-size: 20px;
          color: #22c55e;
        }
        .help-title {
          font-size: 18px;
          font-weight: bold;
          color: var(--el-color-primary);
          [class="dark"] & {
            color: #8cd2ff;
          }
        }
      }
      .content-body {
        flex: 1;
        padding: 0 24px 24px;
        overflow-y: auto;
        .help-content {
          font-size: 14px;
          line-height: 1.7;
          color: var(--el-text-color-regular);
          [class="dark"] & {
            color: #b0b8c7;
          }
          h3 {
            margin: 18px 0 12px;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-color-primary);
            [class="dark"] & {
              color: #8cd2ff;
            }
          }
          h4 {
            margin: 16px 0 10px;
            font-size: 14px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            [class="dark"] & {
              color: #b0b8c7;
            }
          }
          ul {
            padding: 0;
            margin: 0 0 12px 18px;
            list-style: disc;
            li {
              margin-bottom: 8px;
              strong {
                font-weight: 600;
                color: var(--el-text-color-primary);
                [class="dark"] & {
                  color: #b0b8c7;
                }
              }
            }
          }
          p {
            margin-bottom: 12px;
          }
          img {
            display: block;
            max-width: 100%;
            height: auto;
            margin: 0 auto;
          }
        }
      }
    }
  }
}

// 滚动条样式
.help-sidebar .sidebar-content::-webkit-scrollbar,
.help-content-area .content-body::-webkit-scrollbar {
  width: 6px;
}
.help-sidebar .sidebar-content::-webkit-scrollbar-track,
.help-content-area .content-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.help-sidebar .sidebar-content::-webkit-scrollbar-thumb,
.help-content-area .content-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
.help-sidebar .sidebar-content::-webkit-scrollbar-thumb:hover,
.help-content-area .content-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
.help-divider {
  margin-bottom: 8px !important;
}
</style>
