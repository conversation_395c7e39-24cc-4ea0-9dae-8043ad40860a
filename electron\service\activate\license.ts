"use strict";

const { logger } = require("ee-core/log");

import { ErrorCode, myAuthMgr } from "license";
import IECCONSTANTS from "../../data/debug/iecConstants";

/**
 * LicenseCheck SERVICE
 * <AUTHOR>
 * @class
 */
class LicenseService {
  
  // 返回机器码
  async getMachineCode(): Promise<string> {
    logger.info("LicenseService getMachineCode");
    var machineCode = await myAuthMgr.getMachineCode();
    return machineCode;
  }
  // 检查校验码
  async checkAuth(): Promise<boolean> {
    logger.info("LicenseService checkAuth");
    const SOFTWARE_NAME = IECCONSTANTS.PRODUCTNAME; // 软件名称
    var checkResult = await myAuthMgr.checkAuth(SOFTWARE_NAME);
    console.log(checkResult.errInfo);
    if (checkResult.errCode === ErrorCode.SUCCESS) {
      return true;
    } else {
      console.log(checkResult.errInfo);
      // 获取业务错误信息
      throw new Error(String(checkResult?.errInfo));
    }
  }

  // 检查校验码
  async activate(activaeCode: string): Promise<boolean> {
    logger.info("LicenseService activate");
    const SOFTWARE_NAME = IECCONSTANTS.PRODUCTNAME;
    var checkResult = await myAuthMgr.activate(SOFTWARE_NAME, activaeCode);
    console.log(checkResult.errInfo);
    if (checkResult.errCode === ErrorCode.SUCCESS) {
      return true;
    } else {
      console.log(checkResult.errInfo);
      // 获取业务错误信息
      throw new Error(String(checkResult?.errInfo));
    }
  }
}

LicenseService.toString = () => "[class LicenseService]";
const licenseService = new LicenseService();

export { LicenseService, licenseService };
