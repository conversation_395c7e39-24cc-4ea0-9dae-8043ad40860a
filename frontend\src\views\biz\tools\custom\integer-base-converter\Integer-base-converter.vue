<script setup lang="ts">
import InputCopyable from "@/components/Tools/InputCopyable.vue";
import { convertBase } from "./integer-base-converter.model";
import { getErrorMessageIfThrows } from "@/utils/error";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const inputProps = {
  labelPosition: "left",
  labelWidth: "120px",
  labelAlign: "right",
  readonly: true
} as const;

const input = ref("1");
const inputBase = ref(10);
const outputBase = ref(20);

function errorlessConvert(...args: Parameters<typeof convertBase>): string {
  try {
    return convertBase(...args);
  } catch (err) {
    return "";
  }
}

const error = computed(() => getErrorMessageIfThrows(() => convertBase({ value: input.value, fromBase: inputBase.value, toBase: outputBase.value })));

const customBasePlaceholder = computed(() => t("tools.radix.customBasePlaceholder", { base: outputBase.value }));

const customBaseLabel = computed(() => `Base ${outputBase.value.toString().padStart(2, " ")}`);
</script>

<template>
  <div class="head">
    {{ t("tools.radix.title") }}
    <div class="sub-head">{{ t("tools.radix.description") }}</div>
  </div>
  <div class="base-converter">
    <div class="converter-card">
      <!-- 输入区域 -->
      <div class="input-section">
        <div class="section-title">
          <span class="title-icon">📝</span>
          {{ t("tools.radix.inputLabel") }}
        </div>
        <c-input-text v-model:value="input" :placeholder="t('tools.radix.inputPlaceholder')" class="input-field" />
        <el-alert v-if="error" type="error" class="error-alert">
          {{ error }}
        </el-alert>
      </div>

      <!-- 分隔线 -->
      <el-divider class="section-divider" />

      <!-- 输出区域 -->
      <div class="output-section">
        <div class="section-title">
          <span class="title-icon">🔢</span>
          {{ t("tools.radix.outputLabel") }}
        </div>

        <div class="results-grid">
          <div class="result-item">
            <InputCopyable
              :label="t('tools.radix.binary')"
              v-bind="inputProps"
              :value="errorlessConvert({ value: input, fromBase: inputBase, toBase: 2 })"
              :placeholder="t('tools.radix.binaryPlaceholder')"
              class="radix-result-input"
            />
          </div>

          <div class="result-item">
            <InputCopyable
              :label="t('tools.radix.octal')"
              v-bind="inputProps"
              :value="errorlessConvert({ value: input, fromBase: inputBase, toBase: 8 })"
              :placeholder="t('tools.radix.octalPlaceholder')"
              class="radix-result-input"
            />
          </div>

          <div class="result-item">
            <InputCopyable
              :label="t('tools.radix.decimal')"
              v-bind="inputProps"
              :value="errorlessConvert({ value: input, fromBase: inputBase, toBase: 10 })"
              :placeholder="t('tools.radix.decimalPlaceholder')"
              class="radix-result-input"
            />
          </div>

          <div class="result-item">
            <InputCopyable
              :label="t('tools.radix.hex')"
              v-bind="inputProps"
              :value="errorlessConvert({ value: input, fromBase: inputBase, toBase: 16 })"
              :placeholder="t('tools.radix.hexPlaceholder')"
              class="radix-result-input"
            />
          </div>

          <div class="result-item">
            <InputCopyable
              :label="t('tools.radix.base64')"
              v-bind="inputProps"
              :value="errorlessConvert({ value: input, fromBase: inputBase, toBase: 64 })"
              :placeholder="t('tools.radix.base64Placeholder')"
              class="radix-result-input"
            />
          </div>
        </div>

        <!-- 自定义进制 -->
        <div class="custom-base-section">
          <div class="custom-base-header">
            <span class="title-icon">⚙️</span>
            {{ t("tools.radix.customBase") }}
          </div>
          <div class="custom-base-row">
            <div class="base-selector">
              <label class="base-label">{{ t("tools.radix.customBase") }}</label>
              <el-input-number v-model="outputBase" :max="64" :min="2" class="base-input" controls-position="right" />
            </div>
            <div class="custom-result">
              <InputCopyable
                :label="customBaseLabel"
                v-bind="inputProps"
                :value="errorlessConvert({ value: input, fromBase: inputBase, toBase: outputBase })"
                :placeholder="customBasePlaceholder"
                class="radix-result-input"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.head {
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  .sub-head {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--el-text-color-regular);
  }
}
.base-converter {
  .converter-card {
    padding: 24px;
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
    transition: box-shadow 0.3s ease;
    &:hover {
      box-shadow: 0 6px 20px rgb(0 0 0 / 8%);
    }
  }
  .section-title {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    .title-icon {
      font-size: 18px;
    }
  }
  .input-section {
    margin-bottom: 24px;
    .input-field {
      :deep(.input-wrapper) {
        height: 48px;
        background: var(--el-bg-color);
        border: 2px solid var(--el-color-primary-light-5);
        border-radius: 8px;
        transition: all 0.2s ease;
        &:hover {
          border-color: var(--el-color-primary-light-3);
          box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
        }
        &:focus-within {
          border-color: var(--el-color-primary);
          box-shadow: 0 0 0 3px var(--el-color-primary-light-8);
        }
        input {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-color-primary);
          text-align: center;
          &::placeholder {
            color: var(--el-color-primary-light-5);
          }
          &::selection {
            background: var(--el-color-primary-light-7);
          }
        }
      }
    }
    .error-alert {
      margin-top: 12px;
      border-radius: 8px;
    }
  }
  .section-divider {
    margin: 24px 0;
    border-color: var(--el-border-color-lighter);
  }
  .output-section {
    // 全局InputCopyable标签样式
    :deep(.input-copyable) {
      .c-input-text {
        .label {
          box-sizing: border-box !important;
          display: inline-block !important;
          flex-shrink: 0 !important;
          width: 120px !important;
          padding-right: 12px !important;
          overflow: hidden !important;
          font-size: 13px !important;
          font-weight: 600 !important;
          color: var(--el-text-color-primary) !important;
          text-align: right !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
        }
      }
    }
    .results-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;
      margin-bottom: 24px;
      .result-item {
        :deep(.input-copyable) {
          .c-input-text {
            .input-wrapper {
              height: 44px;
              background: var(--el-bg-color);
              border: 1px solid var(--el-border-color-light);
              border-radius: 6px;
              transition: all 0.2s ease;
              &:hover {
                border-color: var(--el-border-color-hover);
                box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
              }
              input {
                font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
                font-size: 14px;
                font-weight: 600;
                color: var(--el-text-color-primary);
                text-align: right !important;
                background: transparent;
                &::selection {
                  background: var(--el-color-primary-light-7);
                }
              }
              .input {
                text-align: right !important;
              }
            }
            .label {
              box-sizing: border-box;
              display: inline-block;
              flex-shrink: 0;
              width: 120px !important;
              min-width: 120px !important;
              max-width: 120px !important;
              padding-right: 12px;
              overflow: hidden;
              font-size: 13px;
              font-weight: 600;
              color: var(--el-text-color-primary);
              text-align: right;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
    .custom-base-section {
      padding: 20px;
      background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
      border: 1px solid var(--el-color-primary-light-7);
      border-radius: 8px;
      .custom-base-header {
        display: flex;
        gap: 8px;
        align-items: center;
        margin-bottom: 16px;
        font-size: 15px;
        font-weight: 600;
        color: var(--el-color-primary);
        .title-icon {
          font-size: 16px;
        }
      }
      .custom-base-row {
        display: flex;
        gap: 16px;
        align-items: center;
        .base-selector {
          display: flex;
          flex-shrink: 0;
          gap: 12px;
          align-items: center;
          .base-label {
            font-size: 13px;
            font-weight: 600;
            color: var(--el-color-primary);
            white-space: nowrap;
          }
          .base-input {
            width: 100px;
            :deep(.el-input__inner) {
              font-weight: 600;
              color: var(--el-color-primary);
              text-align: center;
              background: var(--el-bg-color);
              border: 2px solid var(--el-color-primary-light-5);
              border-radius: 6px;
              &:hover {
                border-color: var(--el-color-primary-light-3);
              }
              &:focus {
                border-color: var(--el-color-primary);
                box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
              }
            }
          }
        }
        .custom-result {
          flex: 1;
          :deep(.input-copyable) {
            .c-input-text {
              .label {
                box-sizing: border-box !important;
                display: inline-block !important;
                flex-shrink: 0 !important;
                width: 120px !important;
                min-width: 120px !important;
                max-width: 120px !important;
                padding-right: 12px !important;
                overflow: hidden !important;
                font-size: 13px !important;
                font-weight: 700 !important;
                color: var(--el-color-primary) !important;
                text-align: right !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
              }
              .input-wrapper {
                height: 44px;
                background: var(--el-bg-color);
                border: 2px solid var(--el-color-primary-light-5);
                border-radius: 6px;
                transition: all 0.2s ease;
                &:hover {
                  border-color: var(--el-color-primary-light-3);
                  box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
                }
                &:focus-within {
                  border-color: var(--el-color-primary);
                  box-shadow: 0 0 0 3px var(--el-color-primary-light-8);
                }
                input {
                  font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
                  font-size: 14px;
                  font-weight: 700;
                  color: var(--el-color-primary);
                  text-align: right !important;
                  background: transparent;
                  &::selection {
                    background: var(--el-color-primary-light-7);
                  }
                }
                .input {
                  text-align: right !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .base-converter {
    .converter-card {
      padding: 16px;
    }
    .output-section {
      .custom-base-section {
        .custom-base-row {
          flex-direction: column;
          gap: 12px;
          align-items: stretch;
          .base-selector {
            justify-content: space-between;
            .base-label {
              white-space: normal;
            }
            .base-input {
              width: 120px;
            }
          }
          .custom-result {
            width: 100%;
          }
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .base-converter {
    .converter-card {
      box-shadow: 0 4px 12px rgb(0 0 0 / 20%);
      &:hover {
        box-shadow: 0 6px 20px rgb(0 0 0 / 30%);
      }
    }
  }
}
</style>

<style lang="scss">
/* 全局样式覆盖，确保进制转换结果的输入框右对齐 */
.base-converter .output-section .results-grid .result-item .input-copyable .c-input-text .input-wrapper input,
.base-converter .output-section .custom-base-section .custom-result .input-copyable .c-input-text .input-wrapper input {
  text-align: right !important;
}
.base-converter .output-section .results-grid .result-item .input-copyable .c-input-text .input-wrapper .input,
.base-converter .output-section .custom-base-section .custom-result .input-copyable .c-input-text .input-wrapper .input {
  text-align: right !important;
}

/* 专门针对进制转换结果输入框的样式 */
.radix-result-input {
  :deep(.c-input-text) {
    .input-wrapper {
      input {
        text-align: right !important;
      }
      .input {
        text-align: right !important;
      }
    }
  }
}

/* 保证所有进制转换结果输入框左侧对齐 */
.base-converter .results-grid,
.base-converter .result-item,
.base-converter .result-item .c-input-text,
.base-converter .result-item .c-input-text .input-wrapper {
  box-sizing: border-box;
  width: 100% !important;
}
.base-converter .result-item {
  padding-left: 0 !important;
  margin-left: 0 !important;
}
.base-converter .c-input-text.label-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.base-converter .c-input-text .label {
  width: 120px !important;
  padding-right: 12px;
  text-align: left;
}
.base-converter .c-input-text .input-wrapper {
  flex: 1 1 0;
}
.base-converter .results-grid .c-input-text .input-wrapper input,
.base-converter .custom-base-section .c-input-text .input-wrapper input {
  background: var(--el-fill-color-light) !important; /* 统一为element-plus风格 */
  transition: background 0.2s;
}
.base-converter .results-grid .c-input-text .input-wrapper input:focus,
.base-converter .custom-base-section .c-input-text .input-wrapper input:focus {
  background: var(--el-fill-color-light) !important; /* 聚焦时同样保持 */
}
</style>
